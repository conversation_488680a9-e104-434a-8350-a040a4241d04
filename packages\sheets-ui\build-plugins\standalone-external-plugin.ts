import type { Plugin } from 'vite';

export function standaloneExternalPlugin(): Plugin {
  return {
    name: 'standalone-external',
    enforce: 'pre',
    apply: 'build',

    resolveId(source) {
      // 只将 peer dependencies 标记为外部依赖
      if (source === 'react' || source === 'react-dom' || source === 'rxjs') {
        return { id: source, external: true };
      }
      
      // 所有 @univerjs 包都内联打包
      return null;
    },

    outputOptions(opts) {
      opts.globals = {
        'react': 'React',
        'react-dom': 'ReactDOM',
        'rxjs': 'rxjs'
      };
      return opts;
    },
  };
};
